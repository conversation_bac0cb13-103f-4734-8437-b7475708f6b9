"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

import {
  RiDashboardLine,
  RiRocketLine,
  RiUserLine,
  RiSettings3Line,
  RiBarChartLine,
  RiMenuLine,
} from "@remixicon/react"
import { Tag } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const navigation = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: RiDashboardLine,
    description: "Tổng quan hệ thống",
  },
  {
    name: "Projects",
    href: "/admin/projects",
    icon: RiRocketLine,
    description: "Quản lý dự án",
    badge: "New",
  },
  {
    name: "Users",
    href: "/admin/users",
    icon: RiUserLine,
    description: "Quản lý người dùng",
  },
  {
    name: "Categories",
    href: "/admin/categories",
    icon: Tag,
    description: "Quản lý danh mục",
  },
  {
    name: "Analytics",
    href: "/admin/analytics",
    icon: RiBarChartLine,
    description: "Thống kê & báo cáo",
  },
  {
    name: "Settings",
    href: "/admin/settings",
    icon: RiSettings3Line,
    description: "Cài đặt hệ thống",
  },
]

interface AdminNavProps {
  className?: string
}

export function AdminNav({ className }: AdminNavProps) {
  const pathname = usePathname()

  return (
    <nav className={cn("bg-card border-b", className)}>
      <div className="container mx-auto max-w-6xl px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/admin" className="flex items-center gap-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <RiSettings3Line className="h-4 w-4" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-lg font-bold">Admin Panel</h1>
                <p className="text-xs text-muted-foreground">Open Launch</p>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href || (item.href !== "/admin" && pathname.startsWith(item.href))

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                    "hover:bg-accent hover:text-accent-foreground",
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  <item.icon className="h-4 w-4" />
                  <span>{item.name}</span>
                  {item.badge && (
                    <Badge variant="secondary" className="text-xs ml-1">
                      {item.badge}
                    </Badge>
                  )}
                </Link>
              )
            })}
          </div>

          {/* Mobile Navigation */}
          <div className="md:hidden">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  aria-label="Mở menu admin"
                >
                  <RiMenuLine className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                {navigation.map((item) => {
                  const isActive = pathname === item.href || (item.href !== "/admin" && pathname.startsWith(item.href))
                  
                  return (
                    <DropdownMenuItem key={item.name} asChild>
                      <Link
                        href={item.href}
                        className={cn(
                          "flex items-center gap-3 w-full",
                          isActive && "bg-accent"
                        )}
                      >
                        <item.icon className="h-4 w-4" />
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span>{item.name}</span>
                            {item.badge && (
                              <Badge variant="secondary" className="text-xs">
                                {item.badge}
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {item.description}
                          </p>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </nav>
  )
}
